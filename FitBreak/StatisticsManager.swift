//
//  StatisticsManager.swift
//  FitBreak
//
//  Created by <PERSON> on 16/09/2025.
//

import Foundation
import CoreData
import Combine

struct DailyStatistic {
    let date: Date
    let completedSessions: Int
    let totalFocusTime: TimeInterval
}

@MainActor
class StatisticsManager: ObservableObject {
    @Published var completedSessions: Int = 0
    @Published var totalFocusTime: TimeInterval = 0
    @Published var currentStreak: Int = 0
    @Published var bestStreak: Int = 0
    @Published var averageSessionTime: TimeInterval = 0
    @Published var dailyStats: [DailyStatistic] = []
    @Published var recentSessions: [PomodoroSession] = []
    
    private let persistenceController = PersistenceController.shared
    private let calendar = Calendar.current
    
    var totalFocusTimeFormatted: String {
        formatDuration(totalFocusTime)
    }
    
    var averageSessionTimeFormatted: String {
        formatDuration(averageSessionTime)
    }
    
    func loadStatistics(for timeframe: StatisticsView.TimeFrame) {
        let context = persistenceController.container.viewContext
        let dateRange = getDateRang<PERSON>(for: timeframe)
        
        loadCompletedSessions(in: dateRange, context: context)
        loadTotalFocusTime(in: dateRange, context: context)
        loadAverageSessionTime(in: dateRange, context: context)
        loadDailyStatistics(in: dateRange, context: context)
        loadRecentSessions(context: context)
        loadStreakData(context: context)
    }
    
    private func getDateRange(for timeframe: StatisticsView.TimeFrame) -> (start: Date, end: Date) {
        let now = Date()
        let calendar = Calendar.current
        
        switch timeframe {
        case .day:
            let startOfDay = calendar.startOfDay(for: now)
            let endOfDay = calendar.date(byAdding: .day, value: 1, to: startOfDay)!
            return (startOfDay, endOfDay)
            
        case .week:
            let startOfWeek = calendar.dateInterval(of: .weekOfYear, for: now)?.start ?? now
            let endOfWeek = calendar.date(byAdding: .weekOfYear, value: 1, to: startOfWeek)!
            return (startOfWeek, endOfWeek)
            
        case .month:
            let startOfMonth = calendar.dateInterval(of: .month, for: now)?.start ?? now
            let endOfMonth = calendar.date(byAdding: .month, value: 1, to: startOfMonth)!
            return (startOfMonth, endOfMonth)
            
        case .year:
            let startOfYear = calendar.dateInterval(of: .year, for: now)?.start ?? now
            let endOfYear = calendar.date(byAdding: .year, value: 1, to: startOfYear)!
            return (startOfYear, endOfYear)
        }
    }
    
    private func loadCompletedSessions(in dateRange: (start: Date, end: Date), context: NSManagedObjectContext) {
        let request: NSFetchRequest<PomodoroSession> = PomodoroSession.fetchRequest()
        request.predicate = NSPredicate(
            format: "completedAt >= %@ AND completedAt < %@ AND wasCompleted == YES AND sessionType == %@",
            dateRange.start as NSDate,
            dateRange.end as NSDate,
            "work"
        )
        
        do {
            let sessions = try context.fetch(request)
            completedSessions = sessions.count
        } catch {
            print("Error fetching completed sessions: \(error)")
            completedSessions = 0
        }
    }
    
    private func loadTotalFocusTime(in dateRange: (start: Date, end: Date), context: NSManagedObjectContext) {
        let request: NSFetchRequest<PomodoroSession> = PomodoroSession.fetchRequest()
        request.predicate = NSPredicate(
            format: "completedAt >= %@ AND completedAt < %@ AND wasCompleted == YES AND sessionType == %@",
            dateRange.start as NSDate,
            dateRange.end as NSDate,
            "work"
        )
        
        do {
            let sessions = try context.fetch(request)
            totalFocusTime = sessions.reduce(0) { total, session in
                total + TimeInterval(session.duration)
            }
        } catch {
            print("Error fetching focus time: \(error)")
            totalFocusTime = 0
        }
    }
    
    private func loadAverageSessionTime(in dateRange: (start: Date, end: Date), context: NSManagedObjectContext) {
        let request: NSFetchRequest<PomodoroSession> = PomodoroSession.fetchRequest()
        request.predicate = NSPredicate(
            format: "completedAt >= %@ AND completedAt < %@ AND wasCompleted == YES AND sessionType == %@",
            dateRange.start as NSDate,
            dateRange.end as NSDate,
            "work"
        )
        
        do {
            let sessions = try context.fetch(request)
            if sessions.isEmpty {
                averageSessionTime = 0
            } else {
                let totalTime = sessions.reduce(0) { total, session in
                    total + TimeInterval(session.duration)
                }
                averageSessionTime = totalTime / Double(sessions.count)
            }
        } catch {
            print("Error calculating average session time: \(error)")
            averageSessionTime = 0
        }
    }
    
    private func loadDailyStatistics(in dateRange: (start: Date, end: Date), context: NSManagedObjectContext) {
        var stats: [DailyStatistic] = []
        var currentDate = dateRange.start
        
        while currentDate < dateRange.end {
            let nextDay = calendar.date(byAdding: .day, value: 1, to: currentDate)!
            
            let request: NSFetchRequest<PomodoroSession> = PomodoroSession.fetchRequest()
            request.predicate = NSPredicate(
                format: "completedAt >= %@ AND completedAt < %@ AND wasCompleted == YES AND sessionType == %@",
                currentDate as NSDate,
                nextDay as NSDate,
                "work"
            )
            
            do {
                let sessions = try context.fetch(request)
                let totalTime = sessions.reduce(0) { total, session in
                    total + TimeInterval(session.duration)
                }
                
                stats.append(DailyStatistic(
                    date: currentDate,
                    completedSessions: sessions.count,
                    totalFocusTime: totalTime
                ))
            } catch {
                print("Error fetching daily statistics: \(error)")
                stats.append(DailyStatistic(
                    date: currentDate,
                    completedSessions: 0,
                    totalFocusTime: 0
                ))
            }
            
            currentDate = nextDay
        }
        
        dailyStats = stats
    }
    
    private func loadRecentSessions(context: NSManagedObjectContext) {
        let request: NSFetchRequest<PomodoroSession> = PomodoroSession.fetchRequest()
        request.sortDescriptors = [NSSortDescriptor(keyPath: \PomodoroSession.completedAt, ascending: false)]
        request.fetchLimit = 10
        request.predicate = NSPredicate(format: "wasCompleted == YES")
        
        do {
            recentSessions = try context.fetch(request)
        } catch {
            print("Error fetching recent sessions: \(error)")
            recentSessions = []
        }
    }
    
    private func loadStreakData(context: NSManagedObjectContext) {
        let request: NSFetchRequest<PomodoroSession> = PomodoroSession.fetchRequest()
        request.sortDescriptors = [NSSortDescriptor(keyPath: \PomodoroSession.completedAt, ascending: false)]
        request.predicate = NSPredicate(format: "wasCompleted == YES AND sessionType == %@", "work")
        
        do {
            let sessions = try context.fetch(request)
            calculateStreaks(from: sessions)
        } catch {
            print("Error fetching streak data: \(error)")
            currentStreak = 0
            bestStreak = 0
        }
    }
    
    private func calculateStreaks(from sessions: [PomodoroSession]) {
        guard !sessions.isEmpty else {
            currentStreak = 0
            bestStreak = 0
            return
        }
        
        // Group sessions by day
        let sessionsByDay = Dictionary(grouping: sessions) { session in
            calendar.startOfDay(for: session.completedAt ?? Date())
        }
        
        let sortedDays = sessionsByDay.keys.sorted(by: >)
        
        // Calculate current streak
        var streak = 0
        let today = calendar.startOfDay(for: Date())
        let yesterday = calendar.date(byAdding: .day, value: -1, to: today)!
        
        // Check if there's a session today or yesterday
        if sessionsByDay[today] != nil {
            streak = 1
            var checkDate = calendar.date(byAdding: .day, value: -1, to: today)!
            
            while sessionsByDay[checkDate] != nil {
                streak += 1
                checkDate = calendar.date(byAdding: .day, value: -1, to: checkDate)!
            }
        } else if sessionsByDay[yesterday] != nil {
            streak = 1
            var checkDate = calendar.date(byAdding: .day, value: -1, to: yesterday)!
            
            while sessionsByDay[checkDate] != nil {
                streak += 1
                checkDate = calendar.date(byAdding: .day, value: -1, to: checkDate)!
            }
        }
        
        currentStreak = streak
        
        // Calculate best streak
        var maxStreak = 0
        var tempStreak = 0
        var previousDate: Date?
        
        for day in sortedDays {
            if let prev = previousDate {
                let daysBetween = calendar.dateComponents([.day], from: day, to: prev).day ?? 0
                if daysBetween == 1 {
                    tempStreak += 1
                } else {
                    maxStreak = max(maxStreak, tempStreak)
                    tempStreak = 1
                }
            } else {
                tempStreak = 1
            }
            previousDate = day
        }
        
        bestStreak = max(maxStreak, tempStreak)
    }
    
    private func formatDuration(_ duration: TimeInterval) -> String {
        let hours = Int(duration) / 3600
        let minutes = (Int(duration) % 3600) / 60
        
        if hours > 0 {
            return "\(hours)h \(minutes)m"
        } else {
            return "\(minutes)m"
        }
    }
}
